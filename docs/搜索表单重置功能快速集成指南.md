# 搜索表单重置功能快速集成指南

## 概述

本指南提供了一套通用的、最小侵入的解决方案，让现有的搜索表单组件快速添加重置功能。

## 核心文件

- `src/utils/hooks/useSearchReset.js` - 简单重置 Hook
- `src/utils/hooks/useSearchFormReset.js` - 高级重置 Hook  
- `src/components/SearchResetButton/index.vue` - 重置按钮组件

## 快速开始

### 方案一：最简单的集成方式（推荐）

只需要 3 步，就能为现有搜索表单添加重置功能：

#### 步骤 1：导入 Hook
```javascript
import useSearchReset from '@/utils/hooks/useSearchReset.js'
```

#### 步骤 2：添加重置功能
```javascript
// 在现有的搜索函数后添加这一行
const { resetSearch } = useSearchReset(params, toggleSearch)
```

#### 步骤 3：添加重置按钮
```html
<!-- 在查询按钮旁边添加 -->
<el-button icon="Refresh" @click="resetSearch">重置</el-button>
```

### 完整示例

以 `SearchFormResource.vue` 为例：

```vue
<template>
  <el-form>
    <!-- 现有的表单项 -->
    <el-form-item>
      <el-button type="primary" @click="toggleSearch">查询</el-button>
      <el-button icon="Refresh" @click="resetSearch">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
// 1. 添加导入
import useSearchReset from '@/utils/hooks/useSearchReset.js'

// 现有代码...
const params = reactive({
  year: '2024',
  dataSource: '1',
  province: ''
})

function toggleSearch() {
  emit('change', params)
}

// 2. 添加重置功能 - 只需要这一行！
const { resetSearch } = useSearchReset(params, toggleSearch)
</script>
```

## 实际应用案例

### 案例 1：商用车数据搜索表单

**文件**: `src/views/ambience/market/vehicle/components/SearchFormResource.vue`

**改动**:
```diff
+ import useSearchReset from '@/utils/hooks/useSearchReset.js'

  const params = reactive({ ...toRaw(props.params) })
  
+ // 使用通用重置 Hook
+ const { resetSearch } = useSearchReset(params, toggleSearch, {
+   beforeReset: () => {
+     // 重置前的自定义逻辑
+     const dataSourceLabel = store.state.dicts.dictsDataSource.find(v => v.value === params.dataSource)?.label
+     if (dataSourceLabel) {
+       changeDataSource(dataSourceLabel)
+     }
+   }
+ })
```

**模板改动**:
```diff
  <el-button type="primary" @click="toggleSearch">查询</el-button>
+ <el-button icon="Refresh" @click="resetSearch">重置</el-button>
```

### 案例 2：发动机数据搜索表单

**文件**: `src/views/home/<USER>/components/SearchFormResource.vue`

**改动**:
```diff
+ import useSearchReset from '@/utils/hooks/useSearchReset.js'

  const params = reactive({ ...toRaw(props.params) })
  
+ // 添加重置功能 - 只需要这一行！
+ const { resetSearch } = useSearchReset(params, toggleSearch)
```

## 高级配置

### 自定义重置逻辑

```javascript
const { resetSearch } = useSearchReset(params, toggleSearch, {
  // 自定义初始值
  initialValues: {
    year: '2023',
    dataSource: '2'
  },
  // 重置前回调
  beforeReset: async () => {
    console.log('准备重置')
    await someAsyncOperation()
  },
  // 重置后回调
  afterReset: () => {
    console.log('重置完成')
  },
  // 是否自动搜索
  autoSearch: true
})
```

### 复杂重置策略

对于复杂的搜索表单，使用高级 Hook：

```javascript
import useSearchFormReset from '@/utils/hooks/useSearchFormReset.js'

const { resetSearch } = useSearchFormReset({
  params,
  searchCallback: toggleSearch,
  resetStrategy: {
    preserveFields: ['dataSource'],     // 保留数据源
    resetToEmpty: ['province'],         // 省份重置为空
    resetToDefault: {                   // 特定默认值
      year: '2024',
      pointerType: '2'
    }
  }
})
```

## 迁移检查清单

在为现有组件添加重置功能时，请检查以下项目：

- [ ] 搜索参数对象是响应式的（使用 `reactive()` 或 `ref()`）
- [ ] 搜索函数能正确触发数据更新
- [ ] 确定需要保留哪些字段（如数据源、板块等）
- [ ] 确定哪些字段需要特殊的重置逻辑
- [ ] 测试重置后的搜索功能是否正常

## 常见问题

### Q: 重置后某些字段没有恢复到期望的值？
A: 检查是否需要使用 `resetStrategy` 配置特定字段的重置行为。

### Q: 重置后搜索没有自动触发？
A: 确保传入的 `searchCallback` 函数正确，或设置 `autoSearch: false` 手动控制。

### Q: 如何保留某些字段不被重置？
A: 使用 `preserveFields` 配置：
```javascript
const { resetSearch } = useSearchFormReset({
  params,
  searchCallback: toggleSearch,
  resetStrategy: {
    preserveFields: ['dataSource', 'segment']
  }
})
```

### Q: 如何让年份字段重置为动态的最大年份？
A: 传入 `dateRange` 对象：
```javascript
// 获取 dateRange
const { dateRange } = useInnerData(params, toggleSearch)

// 配置重置功能
const { resetSearch } = useSearchReset(params, toggleSearch, {
  dateRange // 重置时年份会自动设置为 dateRange.maxYear
})
```

## 兼容性

- ✅ Vue 3 Composition API
- ✅ Element Plus
- ✅ 现有的搜索表单组件
- ✅ 支持异步操作
- ✅ 支持自定义重置逻辑

## 总结

这套解决方案的优势：

1. **最小侵入**: 只需要添加 1-3 行代码
2. **高度可配置**: 支持各种重置策略
3. **向后兼容**: 不影响现有功能
4. **可复用**: 一次开发，到处使用
5. **易于维护**: 统一的重置逻辑管理

通过这套工具，您可以快速为项目中的所有搜索表单添加统一的重置功能，提升用户体验。
