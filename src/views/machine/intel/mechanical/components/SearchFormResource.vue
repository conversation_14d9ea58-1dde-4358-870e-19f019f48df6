<template>
  <div style="overflow: hidden">
    <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
      <el-row :gutter="16">
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="year">
            <el-date-picker
              v-model="params.year"
              type="year"
              value-format="YYYY"
              format="YYYY"
              :disabled-date="disabledFeatureDate"
              placeholder="年份"
              :clearable="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="pointerType">
            <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
              <el-option
                v-for="item in dictsPointerType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="8" :sm="8" :md="3">
          <!-- TODO: 指标类型字典字典变换需要注意修改 -->
          <el-form-item v-if="params.pointerType === '2'" prop="month">
            <el-select v-model="params.month" placeholder="月累" style="width: 100%">
              <el-option
                v-for="item in newDictsMonthTotal"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
            <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
              <el-option
                v-for="item in newDictsQuarter"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-else prop="month">
            <el-select v-model="params.month" placeholder="月度" style="width: 100%">
              <el-option
                v-for="item in newDictsMonth"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <DictsResource
          :form="params"
          :dicts="data.linkageData"
          :props="[
            {
              name: '数据来源',
              key: 'dataSource',
              disabled: true
            },
            {
              name: '板块',
              key: 'segment',
              disabled: true
            },
            {
              name: '细分市场一',
              hide: true,
              key: 'subMarket1'
            },
            {
              name: '细分市场二',
              hide: true,
              key: 'subMarket2'
            }
          ]"
          :propsManuFacturer="{ name: '主机厂', key: 'manuFacturer', show: true, clearable: true }"
          :propsEngineFactory="{ name: '发动机厂', key: 'engineFactory', show: true }"
          :propsFuelType="{ name: '燃料', key: 'fuelType', show: false, type: 'B' }"
          :propsBreed="{ name: '品系', key: 'breed', show: false, disabled: true }"
          @dictsManuFacturer="getDictsManuFacturer"
          :xs="8"
          :sm="8"
          :md="3"
        />
        <el-col :xs="8" :sm="8" :md="6">
          <el-form-item>
            <el-button type="primary" @click="toggleSearch">查询</el-button>
            <!-- 在查询按钮旁边添加 -->
            <el-button icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-segmented
      ref="segmentedRef"
      v-model="params.manuFacturerTabs"
      :options="comDictsManuFacturer"
      @change="toggleSearch('segmented')"
      class="segmented"
    >
      <template #default="scope">
        <div>{{ scope.item.label }}</div>
      </template>
    </el-segmented>
  </div>
</template>

<script setup>
// 上险数没有主机厂
import { throttle } from '@/utils'
import DictsResource from '@/views/components/DictsResource.vue'
import useInnerData from '@/utils/hooks/innerData.js'
import useSearchReset from '@/utils/hooks/useSearchReset.js'
import { dictsPointerType, dictDataType } from '@/utils/common/dicts.js'

// const dictsPointerType = [
//   {
//     value: '0',
//     label: '月度'
//   },
//   {
//     value: '1',
//     label: '季度'
//   }
// ]
const store = useStore()

// 通机tabs默认值
const defaultDictsManuFacturerTong = [
  '三一集团',
  '徐工集团',
  '柳工集团',
  '临工集团',
  '中联重科',
  '杭叉集团',
  '江苏沃得',
  '常州东风',
  '江苏常发',
  '河北英虎'
]
const emit = defineEmits(['change'])

const segmentedRef = ref(null)
const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      month: '', // 月
      quarter: '', // 季度
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      dataSource: '', // 数据来源
      subMarket1: '', // 细分市场1
      manuFacturerTabs: '', // 主机厂
      manuFacturer: '', // 主机厂
      engineFactory: '', // 发动机厂
      breed: '', // 品系
      segment: '' // 板块
    })
  }
})
const data = reactive({
  linkageData: [], // 多级联动数据
  dictsManuFacturer: []
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)

// 重置搜索条件
const resetSearch = async () => {
  // 重置表单到初始值
  const initialValues = { ...toRaw(props.params) }
  Object.assign(params, initialValues)

  // 重新初始化装机数数据源
  await initDateRange('装机数')

  // 重置主机厂标签页
  if (params.segment === '通机') {
    await getDictsManuFacturer()
  }

  // 自动执行搜索
  toggleSearch()
}
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)

// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch(ev) {
  if (ev === 'segmented') {
    params.manuFacturer = params.manuFacturerTabs
  } else {
    params.manuFacturerTabs = params.manuFacturer
  }
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  delete data.subMarket2
  emit('change', data)
}
const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['装机数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}

let length = ref(data.dictsManuFacturer.length)
// 固定不在动态添加其他主机厂
const getDictsManuFacturer = async ev => {
  // const dicts = JSON.parse(JSON.stringify(ev))
  const newDicts = [] // 展示的主机厂
  // 排在后面的字典
  let lastDicts = []
  if (params.segment === '通机') {
    // lastDicts = dicts.filter(
    //   item => defaultDictsManuFacturerTong.findIndex(el => el !== item.label) !== -1
    // )
    defaultDictsManuFacturerTong.forEach(el => {
      newDicts.push({ label: el, value: el })
    })
  }

  // newDicts.push(...lastDicts)
  data.dictsManuFacturer = newDicts
  length = ref(data.dictsManuFacturer.length)
  await nextTick()
  params.manuFacturer = newDicts && newDicts.length > 0 ? newDicts[0].value : ''

  resizeDom()
}

const comDictsManuFacturer = computed(() => {
  return data.dictsManuFacturer.slice(0, length.value)
})
function getPercent() {
  const curWidth = segmentedRef.value.$el.offsetWidth
  const parentWidth = segmentedRef.value.$parent.$el.offsetWidth
  return parentWidth / curWidth
}
const resizeDom = throttle(() => {
  if (!segmentedRef.value) return
  const percent = getPercent()
  length.value = Math.floor(comDictsManuFacturer.value.length * percent.toFixed(2))
  nextTick(() => {
    const percent = getPercent()
    if (percent < 1) {
      length.value--
    }
  })
})

onMounted(() => {
  resizeDom()
  if (window.ResizeObserver) {
    // 使用 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      resizeDom()
    })

    // 开始监听容器大小变化
    resizeObserver.observe(segmentedRef.value.$parent.$el)
  } else {
    window.addEventListener('resize', () => {
      resizeDom()
    })
  }
})

initDateRange('装机数', true)
getDictsData()
</script>

<style scoped lang="scss">
.search-form {
  margin-bottom: 0;
}
.segmented {
  margin: 10px 20px;
}
</style>
