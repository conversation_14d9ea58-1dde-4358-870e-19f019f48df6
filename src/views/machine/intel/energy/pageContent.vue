<template>
  <div>
    <BiContent>
      <template #searchArea>
        <SearchFormResource
          :params="data.params"
          @change="getParams"
          :comDictsManuFacturerList="comDictsManuFacturerList"
        />
      </template>
      <el-row :gutter="20" style="margin-left: 0; margin-right: 0">
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <yc-chart
            :height="device != 'desktop' ? '60vh' : '40vh'"
            v-loading="loading1"
            :formatter="params => NodeToHtml(NTips, { params, showTotal: false })"
            :title="{ text: `${data.params.manuFacturer}细分车型销量趋势` }"
            :options="configChartA"
            :use-default-options="false"
            :key="configChartA.series.length"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <yc-chart
            v-loading="loading2"
            :height="device != 'desktop' ? '60vh' : '40vh'"
            :formatter="params => NodeToHtml(NTips, { params, showTotal: false })"
            :title="{ text: `${data.params.manuFacturer}细分市场销量趋势` }"
            :options="configChartB"
            :use-default-options="false"
            :key="configChartB.series.length"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <chart
            v-loading="loading3"
            :height="device != 'desktop' ? '60vh' : '40vh'"
            :title="{ text: `${data.params.manuFacturer}燃料结构趋势` }"
            :options="configChartC"
            :use-default-options="false"
            :precision="0"
            :key="configChartC.series.length"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <yc-chart
            v-loading="loading4"
            :height="device != 'desktop' ? '60vh' : '40vh'"
            :formatter="
              params =>
                TooltipFormatter(TooltipPercentageComponent, params, {
                  singleColumn: false,
                  sortField: 'value'
                })
            "
            :title="{ text: `${data.params.manuFacturer}动力配套结构趋势` }"
            :options="configChartD"
            :use-default-options="false"
            :key="configChartD.series.length"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <chart
            :key="configChartE.series.length"
            v-loading="loading5"
            height="40vh"
            :title="{ text: `${data.params.manuFacturer}省份区域销量` }"
            :options="configChartE"
            :precision="0"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <chart
            v-loading="loading6"
            height="40vh"
            :title="{ text: `${data.params.manuFacturer}TOP10城市销量` }"
            :options="configChartF"
            :key="configChartF.series.length"
            :precision="0"
          />
        </el-col>
      </el-row>
    </BiContent>
  </div>
</template>

<script setup lang="jsx">
import SearchFormResource from './components/SearchFormResource.vue'
import chart from '@/views/components/echarts/index.vue'
import YcChart from '@/views/components/echarts/customIndex.vue'
import hooksChartsConfig from './chartsConfig.js'
import { TooltipFormatter, sortChartLegend } from '@/utils/common/method.js'
import Tpis from '@/views/components/tooltip/index.vue'
import NTips from './components/tips.vue'
import BiContent from '@/views/components/tabs/BiContent/index.vue'

import {
  hostNewEnergyWeightYearDataApi,
  hostNewEnergyWeightMonthDataApi,
  hostNewEnergyBreedYearDataApi,
  hostNewEnergyBreedMonthDataApi,
  hostNewEnergyDieselYearDataApi,
  hostNewEnergyDieselMonthDataApi,
  hostNewEnergyEngineYearDataApi,
  hostNewEnergyEngineMonthDataApi,
  hostNewEnergyProvinceDataApi,
  hostNewEnergyCityDataApi
} from '@/api/machine/energy'
import { parseInt } from 'lodash'
// import Tips from './components/tips.vue'
import { TooltipComponent } from '@/views/components/jsx/TooltipComponent.jsx'

import { NodeToHtml } from '@/utils/common/method.js'
import { getManufacturersByRank } from '../../../../api/machine/energy.js'
import { cloneDeep } from 'lodash'
import { ref } from 'vue'
import { setYuchaiColor } from '@/utils/common/method.js'
import { numberFormat } from '@/utils/format.js'
const store = useStore()
const device = computed(() => store.state.biapp.device)
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '上险数')?.value
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)
const loading4 = ref(false)
const loading5 = ref(false)
const loading6 = ref(false)

const barWidth = ref('20')
// return data.params.pointerType === 1 ? '20' : '10'

// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  quarter: '1', // 季度
  pointerType: '0', // 指标类型(0-月，2-月累，1-季度)
  dataSource: dataSource, // 数据来源（货运新增）
  subMarket1: '卡车', // 细分市场1
  manuFacturer: '徐工', // 主机厂
  engineFactory: '', // 发动机厂111
  fuelType: '', // 燃料
  vehicleType: '', // 车型
  cylinders: '', // 气缸数
  breed: '', // 品系
  province: '', // 省
  segment: '商用车' // 板块
}
const comDictsManuFacturerList = ref()

const data = reactive({
  params: { ...originParams }
})

const {
  configChartA,
  configChartB,
  configChartC,
  configChartD,
  configChartE,
  configChartF,
  setSeriesData
} = hooksChartsConfig()

const quarterArray = {
  Q1: '第一季度',
  Q2: '第二季度',
  Q3: '第三季度',
  Q4: '第四季度'
}

async function getParams(params) {
  // params.year = 2024
  data.params = { ...params }
  initChartData()
}
let previousSubMarket1 = data.params.subMarket1

/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = async () => {
  configChartA.series = []
  configChartB.series = []
  configChartC.series = []
  configChartD.series = []
  configChartE.series = []
  configChartF.series = []
  // console.log('data.params', data.params)
  const params = JSON.parse(JSON.stringify(data.params))
  params.year = parseInt(params.year)

  // --- 图表请求绘图数据
  loading1.value = true
  Promise.all([
    hostNewEnergyWeightYearDataApi(params), // 第一个图表left
    hostNewEnergyWeightMonthDataApi(params) // 第一个图表right
  ])
    .then(([res1, res2]) => {
      if (res1.code == '200') {
        let { configChart, seriesL } = setSeriesBarData(res1, configChartA)
        configChartA.series = [...configChart.series, ...seriesL]
      }
      if (res2.code == '200') {
        const { patternRsList, yoyRsList } = res2.data
        // b.右边堆叠图按销量显示⾼度，⿏标悬浮显示同⽐、销量、占⽐; 折线显示同⽐中当前选择的主机⼚
        const allData = []
        patternRsList.forEach(patternRs => {
          // 设置x轴name y轴value
          patternRs.forEach(v => {
            v.name = v.month + '月'
            v.value = v.installationcount
            v.monthName = data.params.pointerType === '1' ? quarterArray[v.month] : v.month + '月'
            allData.push(cloneDeep(v))
          })
        })

        const seriesR = setSeriesData({
          list: allData,
          xAxisKey: 'monthName',
          yAxisKey: 'installationcount',
          legendKey: 'type'
        })

        seriesR.forEach(el => {
          el.type = 'bar'
          el.xAxisIndex = 1
          el.yAxisIndex = 1
          el.barMaxWidth = 30
          // el.barWidth =  '10';
          el.stack = 'salesBarList'
        })
        // dat.params.
        // 柱形图
        yoyRsList.total.forEach(el => {
          el.legend = `${data.params.manuFacturer}同比`
          el.monthName = el.month + '月'
          el.value = el.growth_rate
        })
        const seriesLineR = setSeriesData({
          list: yoyRsList.total,
          xAxisKey: 'monthName',
          yAxisKey: 'growth_rate',
          legendKey: 'legend'
        })
        seriesLineR.forEach(el => {
          el.type = 'line'
          el.xAxisIndex = 1
          el.yAxisIndex = 2
        })
        // 设置y轴name
        // configChartA.yAxis[1].name = "单位：(%)"
        // configChartA.yAxis[2].name = '单位：(台)'
        // configChartA.yAxis[2].axisLabel.formatter = '{value}台'
        // 设置x轴name
        configChartA.xAxis[1].data = (
          seriesR[0] ? (seriesR[0].data ? seriesR[0].data : []) : []
        ).map(v => v.name)
        const seriesRList = seriesR.filter(el => el.name !== '')
        configChartA.series = [...configChartA.series, ...seriesRList, ...seriesLineR]
        const truckTypes = ['轻卡', '中卡', '重卡']
        const hasTruckTypes = seriesRList.some(el => truckTypes.includes(el.name))
        configChartA.legend.data = sortChartLegend(configChartA.series, hasTruckTypes, truckTypes)
        console.log('hasTruckTypes', configChartA.legend.data)
      }
    })
    .finally(() => {
      loading1.value = false
    })

  // 第二个图表
  loading2.value = true
  Promise.all([hostNewEnergyBreedYearDataApi(params), hostNewEnergyBreedMonthDataApi(params)])
    .then(([res1, res2]) => {
      if (res1.code == '200') {
        let { configChart, seriesL } = setSeriesBarData(res1, configChartB)
        configChartB.series = [...configChart.series, ...seriesL]
      }
      if (res2.code == '200') {
        const { patternRsList, yoyRsList } = res2.data

        // b.右边按查询条件显示燃料占⽐
        const allData = []
        patternRsList.forEach(patternRs => {
          patternRs.forEach(v => {
            v.name = v.month + '月'
            v.value = v.installationcount
            v.monthName = data.params.pointerType === '1' ? quarterArray[v.month] : v.month + '月'
            allData.push(cloneDeep(v))
          })
        })

        const seriesR = setSeriesData({
          list: allData,
          xAxisKey: 'monthName',
          yAxisKey: 'installationcount',
          legendKey: 'type'
        })
        seriesR.forEach(el => {
          el.type = 'bar'
          el.xAxisIndex = 1
          el.yAxisIndex = 1
          el.barMaxWidth = 30
          // el.barWidth = '10';
          el.stack = 'salesBarList'
        })
        yoyRsList.total.forEach(el => {
          el.legend = `${data.params.manuFacturer}同比`
          el.monthName = el.month + '月'
          el.value = el.growth_rate
        })
        const seriesLineR = setSeriesData({
          list: yoyRsList.total,
          xAxisKey: 'monthName',
          yAxisKey: 'growth_rate',
          legendKey: 'legend'
        })
        seriesLineR.forEach(el => {
          el.type = 'line'
          el.xAxisIndex = 1
          el.yAxisIndex = 2
        })
        configChartB.xAxis[1].data = (
          seriesR[0] ? (seriesR[0].data ? seriesR[0].data : []) : []
        ).map(v => v.name)
        const seriesRList = seriesR.filter(el => el.name !== '')
        configChartB.series = [...configChartB.series, ...seriesRList, ...seriesLineR]

        // 牵引车、载货车、专用车、自卸车、轻卡、皮卡
        const vehicleTypes = ['牵引车', '载货车', '专用车', '自卸车', '轻卡(3.5T以上)', '皮卡']
        const hasVehicleTypes = seriesRList.some(
          el => vehicleTypes.includes(el.name) && el.name !== '3.5吨以下轻卡及其他'
        )
        if (hasVehicleTypes) {
          // 过滤掉不在vehicleTypes中的选项
          const filteredSeries = configChartB.series.filter(el => vehicleTypes.includes(el.name))

          const sortedLegend = filteredSeries
            .map(el => el.name)
            .sort((a, b) => {
              const indexA = vehicleTypes.indexOf(a)
              const indexB = vehicleTypes.indexOf(b)

              // 如果两个都在vehicleTypes中，按vehicleTypes顺序排序
              if (indexA !== -1 && indexB !== -1) {
                return indexA - indexB
              }

              // 如果只有一个是vehicleType，vehicleType优先
              if (indexA !== -1 && indexB === -1) {
                return -1
              }
              if (indexA === -1 && indexB !== -1) {
                return 1
              }

              // 都不是vehicleType，保持原有顺序
              return 0
            })

          configChartB.legend.data = sortedLegend
          // 同时更新series以确保只显示过滤后的数据
          configChartB.series = filteredSeries
        }
      }
    })
    .finally(() => {
      loading2.value = false
    })
  // 第三个图表
  loading3.value = true
  Promise.all([hostNewEnergyDieselYearDataApi(params), hostNewEnergyDieselMonthDataApi(params)])
    .then(([res1, res2]) => {
      if (res1.code == '200') {
        // a.处理左边占⽐
        let { configChart, seriesL } = setSeriesBarData(res1, configChartC)
        configChartC.series = [...configChart.series, ...seriesL]
      }
      if (res2.code == '200') {
        const { patternRsList, yoyRsList } = res2.data
        // b.右边按查询条件显示燃料占⽐
        const allData = []
        patternRsList.forEach(patternRs => {
          patternRs.forEach(v => {
            v.name = v.month + '月'
            v.value = v.installationcount
            v.monthName = data.params.pointerType === '1' ? quarterArray[v.month] : v.month + '月'
            allData.push(cloneDeep(v))
          })
        })
        const seriesR = setSeriesData({
          list: allData,
          xAxisKey: 'monthName',
          yAxisKey: 'installationcount',
          legendKey: 'type'
        })
        seriesR.forEach(el => {
          el.type = 'bar'
          el.xAxisIndex = 1
          el.yAxisIndex = 1
          el.barMaxWidth = 30
          // el.barWidth = '10'
          el.stack = 'salesBarList'
        })
        yoyRsList.total.forEach(el => {
          el.legend = `同比`
          el.monthName = el.month + '月'
          el.value = el.growth_rate
        })
        const seriesLineR = setSeriesData({
          list: yoyRsList.total,
          xAxisKey: 'monthName',
          yAxisKey: 'growth_rate',
          legendKey: 'legend'
        })
        seriesLineR.forEach(el => {
          el.type = 'line'
          el.xAxisIndex = 1
          el.yAxisIndex = 2
        })
        configChartC.xAxis[1].data = (
          seriesR[0] ? (seriesR[0].data ? seriesR[0].data : []) : []
        ).map(v => v.name)
        const seriesRList = seriesR.filter(el => el.name !== '')
        configChartC.series = [...configChartC.series, ...seriesRList, ...seriesLineR]
      }
    })
    .finally(() => {
      loading3.value = false
    })
  // 第四个图表
  loading4.value = true
  Promise.all([hostNewEnergyEngineYearDataApi(params), hostNewEnergyEngineMonthDataApi(params)])
    .then(([res1, res2]) => {
      if (res1.code == '200') {
        let { configChart, seriesL } = setSeriesBarData(res1, configChartD)
        configChartD.series = setYuchaiColor([...configChart.series, ...seriesL])
      }
      if (res2.code == '200') {
        const { patternRsList, yoyRsList } = res2.data
        // b.右边按查询条件显示燃料占⽐
        const allData = []
        patternRsList.forEach(patternRs => {
          patternRs.forEach(v => {
            v.name = v.month + '月'
            v.value = v.installationcount
            v.monthName = data.params.pointerType === '1' ? quarterArray[v.month] : v.month + '月'
            allData.push(cloneDeep(v))
          })
        })
        const seriesR = setSeriesData({
          list: allData,
          xAxisKey: 'monthName',
          yAxisKey: 'installationcount',
          legendKey: 'type'
        })
        seriesR.forEach(el => {
          el.type = 'bar'
          el.xAxisIndex = 1
          el.yAxisIndex = 1
          el.barMaxWidth = 30
          el.stack = 'salesBarList'
        })
        yoyRsList.total.forEach(el => {
          el.legend = `同比`
          el.monthName = el.month + '月'
          el.value = el.growth_rate
        })
        const seriesLineR = setSeriesData({
          list: yoyRsList.total,
          xAxisKey: 'monthName',
          yAxisKey: 'growth_rate',
          legendKey: 'legend'
        })
        seriesLineR.forEach(el => {
          el.type = 'line'
          el.xAxisIndex = 1
          el.yAxisIndex = 2
        })
        configChartD.xAxis[1].data = (
          seriesR[0] ? (seriesR[0].data ? seriesR[0].data : []) : []
        ).map(v => v.name)
        const seriesRList = seriesR.filter(el => el.name !== '')
        configChartD.series = [...configChartD.series, ...seriesRList, ...seriesLineR]
      }
    })
    .finally(() => {
      loading4.value = false
    })
  loading5.value = true

  // 第五个图表
  hostNewEnergyProvinceDataApi(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      configChartE.series = []

      const { patternRsList, yoyRsList } = resData

      const allData = []
      patternRsList.forEach(patternRs => {
        // 设置x轴name y轴value
        patternRs.forEach(v => {
          v.name = v.type
          v.value = v.total_installation
          allData.push(cloneDeep(v))
        })
      })

      yoyRsList.forEach(el => {
        el.name = el.type
        el.value = el.yuchai_proportion
      })

      // 设置series分类name 分类数据data
      const series = []
      // 年数据
      const years = [...new Set(allData.map(v => v.month))].sort((a, b) => a - b)
      let sortProvince = [] // 按销量排序的省份
      years.forEach(ele => {
        const data = allData.filter(v => v.month === ele).sort((a, b) => b.value - a.value)
        if (data.length > sortProvince.length) {
          // 最大值
          sortProvince = data.map(v => v.type)
        }
        series.push({ name: ele, data })
      })
      series.forEach(item => {
        if (item.data.length !== sortProvince.length) {
          const newItemData = []
          sortProvince.forEach(el => {
            const newItemDataJson = item.data.find(v => v.name === el)
            newItemData.push({ name: el, value: newItemDataJson ? newItemDataJson.value : 0 })
          })
          item.data = newItemData
        }
        // 分类名称+年
        item.name = item.name + '年'
        // 设置是柱状图
        item.type = 'bar'
        // 设置分类间距0
        item.barGap = '0'
      })
      // const color = ['#6d99cb', '#e08444']
      // for (let i = 0; i < series.length; i++) {
      //   series[i].itemStyle = {
      //     color: color[i]
      //   }
      // }
      // 占比折线数据添加
      const lineData = []
      sortProvince.forEach(el => {
        const item = yoyRsList.find(v => v.name === el) || { name: el, value: 0 }
        lineData.push(item)
      })
      if (yoyRsList.length > 0) {
        series.push({
          name: `${yoyRsList[0]?.month}年玉柴占比`,
          type: 'line',
          yAxisIndex: 1,
          data: lineData,
          itemStyle: {
            color: '#E72331'
          }
        })
      }
      configChartE.series = series
    })
    .finally(() => {
      loading5.value = false
    })
  loading6.value = true

  // 第六个图表
  hostNewEnergyCityDataApi(params)
    .then(({ code, data: resData }) => {
      if (code != '200') return
      const { patternRsList, yoyRsList } = resData

      const allData = []
      patternRsList.forEach(patternRs => {
        // 设置x轴name y轴value
        patternRs.forEach(v => {
          v.name = v.type
          v.value = v.installationcount
          allData.push(cloneDeep(v))
        })
      })

      yoyRsList.forEach(el => {
        el.name = el.type
        el.value = el.yoy
      })

      // 设置series分类name 分类数据data
      const series = []
      // 年数据
      const years = [...new Set(allData.map(v => v.month))].sort((a, b) => a - b)
      let sortProvince = [] // 按销量排序的省份
      years.forEach(ele => {
        // const data = allData.filter(v => v.month === ele).sort((a, b) => b.value - a.value)
        const data = allData.filter(v => v.month === ele)
        // if (data.length > sortProvince.length) {
        //   // 最大值
        sortProvince = data.map(v => v.type)
        // }

        series.push({ name: ele, data })
      })
      series.forEach(item => {
        if (item.data.length !== sortProvince.length) {
          const newItemData = []
          sortProvince.forEach(el => {
            const newItemDataJson = item.data.find(v => v.name === el)
            newItemData.push({ name: el, value: newItemDataJson ? newItemDataJson.value : 0 })
          })
          item.data = newItemData
        }
        // 分类名称+年
        item.name = item.name + '年'
        // 设置是柱状图
        item.type = 'bar'
        // 设置分类间距0
        item.barGap = '0'
      })
      // const color = ['#6d99cb', '#e08444']
      // for (let i = 0; i < series.length; i++) {
      //   series[i].itemStyle = {
      //     color: color[i]
      //   }
      // }
      // 占比折线数据添加
      const lineData = []
      sortProvince.forEach(el => {
        const item = yoyRsList.find(v => v.name === el) || { name: el, value: 0 }
        lineData.push(item)
      })
      if (yoyRsList.length > 0) {
        series.push({
          name: `${yoyRsList[0]?.current_year}年同比`,
          type: 'line',
          yAxisIndex: 1,
          data: lineData
        })
      }

      configChartF.series = setYuchaiColor(series)
    })
    .finally(() => {
      loading6.value = false
    })
}

/**
 * 设置左边柱状图数据
 */
function setSeriesBarData(res, configChart) {
  const { patternRsList } = res.data
  // a.处理左边占⽐显示
  const flatData = patternRsList.flat()

  const groupedData = {}
  flatData.forEach(item => {
    if (!groupedData[item.month]) {
      groupedData[item.month] = {}
    }
    groupedData[item.month][item.type] = item
  })

  // Step 3: Convert grouped data to ECharts format
  const months = Object.keys(groupedData).sort()
  const series = []
  const types = new Set(flatData.map(item => item.type))

  types.forEach(type => {
    const serieData = months.map(month => {
      const item = groupedData[month][type] || {}
      return {
        name: month + '年',
        value: item.proportion || 0,
        ...item
      }
    })
    series.push({
      name: type,
      type: 'bar',
      xAxisIndex: 0,
      yAxisIndex: 0,
      barWidth: '20',
      stack: 'patternRsList',
      data: serieData
    })
  })
  // 设置x轴name
  configChart.xAxis[0].data = months
  // configChart.graphic.style.text = data.params.year
  return { configChart, seriesL: series }
}

// TODO 接口查询留存
const initOptions = async () => {
  const params = JSON.parse(JSON.stringify(data.params))
  params.year = parseInt(params.year)
  const formatManufacturer = item => ({ label: item, value: item })

  // -- 获取top销量数据 --
  try {
    const res = await getManufacturersByRank({
      year: params.year,
      subMarket1: params.subMarket1,
      dataSource: params.dataSource
    })

    if (res.code === 200) {
      const list = res?.data?.manufacturersList?.slice(0, 10) || []
      if (list.length > 0) {
        comDictsManuFacturerList.value = list.map(formatManufacturer)
      } else {
        comDictsManuFacturerList.value = []
      }
      if (params.subMarket1 !== previousSubMarket1) {
        data.params.manuFacturer = ''
        previousSubMarket1 = params.subMarket1
      }
      if (comDictsManuFacturerList.value.length != 0 && !list.includes(data.params.manuFacturer)) {
        console.log('comDictsManuFacturerList.value', comDictsManuFacturerList.value)
        data.params.manuFacturer = ''
        params.manuFacturer = comDictsManuFacturerList.value[0].value
        data.params.manuFacturer = comDictsManuFacturerList.value[0].value
      }
    }
    // console.log(!list.includes( data.params.manuFacturer ));
  } catch (err) {
    console.error('获取厂商TOP10失败:', err)
  }
}

// Tooltip组件渲染
// Tooltip组件渲染
const TooltipPercentageComponent = props => {
  return (
    <Tpis {...props}>
      {{
        item: ({ item }) => {
          return (
            <>
              {item.axisIndex == 0 && <span>{numberFormat(item.value, 1) || 0}%</span>}
              {item.axisIndex == 1 && (
                <span>
                  {numberFormat(item.value, item.seriesType == 'line' ? 1 : 0) || 0}
                  {item.seriesType == 'line' && item.componentSubType == 'line' ? '%' : '台'}
                </span>
              )}
            </>
          )
        }
      }}
    </Tpis>
  )
}
</script>

<style lang="scss" scoped>
:deep(.el-col) {
  margin-bottom: 0px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
