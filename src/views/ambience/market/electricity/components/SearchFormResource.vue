<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :span="2" :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }" :lg="{ span: 3 }">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }" :lg="{ span: 3 }">
        <el-form-item prop="pointerType">
          <el-select
            v-model="params.pointerType"
            placeholder="指标类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        :span="2"
        :xs="{ span: 24 }"
        :sm="{ span: 12 }"
        :md="{ span: 6 }"
        :lg="{ span: 3 }"
        v-if="params.pointerType !== '1'"
      >
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="2" :xs="{ span: 24 }" :sm="{ span: 12 }" :md="{ span: 6 }" :lg="{ span: 3 }">
        <el-form-item>
          <el-button type="primary" color="#115E93" @click="toggleSearch">查询</el-button>
          <!-- 在查询按钮旁边添加 -->
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'
import useSearchReset from '@/utils/hooks/useSearchReset.js'

// import formValidate from '@/utils/hooks/formValidate.js'
// const { disabledFeatureDate } = formValidate()

const props = defineProps({
  linkageData: {
    type: Array,
    required: true,
    default: () => []
  }, // 多级联动数据
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      month: '', // 月
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      quarter: '', // 季度
      dataSource: '', // 数据来源
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', // 细分市场2
      engineFactory: '', // 发动机厂
      fuelType: '', // 燃料
      manuFacturer: '', // 主机厂
      breed: '', // 品系
      dataType: [] // 数据分类(汽油、微客、微改、微卡)
    })
  }
})

const emit = defineEmits(['change'])

const data = reactive({
  disabledSubMarket2: false,
  disabledBreed: false
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
// 在现有的搜索函数后添加这一行
const { resetSearch } = useSearchReset(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = toRaw(params)
  console.log(data, 'data')
  emit('change', data)
}
initDateRange('船电数', true)
</script>
