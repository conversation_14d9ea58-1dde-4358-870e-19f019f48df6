<template>
  <CommonTabs :hideList="[2]" title="经济数据">
    
    <template #searchArea>
      <el-form :model="formInline" label-width="0" :inline="true" class="tabs-form">
        <el-row :gutter="16" style="margin-right: unset">
          <el-col :xs="16" :sm="16" :md="6">
            <el-form-item>
              <el-date-picker
                v-model="formInline.date"
                type="monthrange"
                value-format="YYYY-MM"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabledDate="disabledDate"
                range-separator="~"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="8" :sm="8" :md="3">
            <el-form-item>
              <el-button type="primary" @click="onSubmit" :loading="economyLoading">查询</el-button>
              <!-- 在查询按钮旁边添加 -->
              <el-button icon="Refresh" @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <el-row v-loading="economyLoading">
      <el-col :span="24" v-for="(item, index) in economyChartList" :key="index">
        <Chart
          :titleIcon="`data${(index % 6) + 1}`"
          v-bind="{
            ...item,
            heightKey: 'vehicle'
          }"
        />
      </el-col>
    </el-row>
  </CommonTabs>
</template>

<script setup>
import Chart from '@/views/ambience/components/CommonChart/Chart'
import CommonTabs from '@/views/components/tabs/CommonTabs'
import { getInitDate3 } from '@/store/modules/macro.js'

let store = useStore()

const formInline = computed(() => store.state.macro.economyParams)
const economyChartList = computed(() => store.state.macro.economyChartList)
const economyLoading = computed(() => store.state.macro.economyLoading)

onMounted(() => {
  store.dispatch('macro/getChartList')
})

const disabledDate = time => {
  return time.getTime() > Date.now()
}

const onSubmit = () => {
  const data = formInline?.value
  store.dispatch('macro/getChartList', data)
}

// 重置搜索条件
const resetSearch = () => {
  // 重置 store 中的参数到初始值
  store.commit('macro/CHANGE_CHARTlIST', {
    key: 'economyParams',
    value: {
      date: getInitDate3()
    }
  })
  // 自动执行搜索
  store.dispatch('macro/getChartList')
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
</style>
