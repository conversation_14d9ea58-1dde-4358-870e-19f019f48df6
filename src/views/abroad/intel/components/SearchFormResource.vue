<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            :clearable="false"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="pointerType">
          <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            disabled: true,
            hide: true,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment',
            hide: true,
            disabled: true,

          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            key: 'subMarket2'
          }
        ]"
        :propsEngineFactory="{ name: '发动机厂', key: 'engineFactory', show: false }"
        :propsFuelType="{ name: '燃料', key: 'fuelType', show: false, type: 'B' }"
        :xs="8"
        :sm="8"
        :md="3"
      />
      <!-- type 燃料类型（A-新能源 B-汽油） -->
      <el-col :span="3">
        <el-form-item>
          <el-button type="primary" @click="toggleSearch">查询</el-button>
          <!-- 在查询按钮旁边添加 -->
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType } from '@/utils/common/dicts.js'
// import formValidate from '@/utils/hooks/formValidate.js'
import useInnerData from '@/utils/hooks/innerData.js'
import useSearchReset from '@/utils/hooks/useSearchReset.js'

const store = useStore()
const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      month: '', // 月
      quarter: '', // 季度
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', //  细分市场2
      manuFacturer: '', // 主机厂
      dataSource: '' // 数据来源（海关数）
    })
  }
})

// const { disabledFeatureDate } = formValidate()
const emit = defineEmits(['change'])
const data = reactive({
  linkageData: [] // 多级联动数据
})
const params = reactive({ ...toRaw(props.params) })
const currentMonth = (new Date().getMonth() + 1).toString() // 月
const currentQuarter = getCurrentQuarter().toString() // 季度
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
// 在现有的搜索函数后添加这一行
const { resetSearch } = useSearchReset(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)
function getCurrentQuarter() {
  const now = new Date()
  const month = now.getMonth() // 0-11
  return Math.floor(month / 3) + 1 // 1-4
}
/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  emit('change', data)
}

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['海关数据']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}
initDateRange('海关数据', true)
getDictsData()
</script>
