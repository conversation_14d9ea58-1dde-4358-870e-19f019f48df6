<template>
  <div class="wrap">
    <el-affix target=".wrap" :offset="navBottom" z-index="999">
      <SearchFormResource :params="data.params" @change="getParams" />
    </el-affix>

    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12">
        <rank v-loading="loading.topList" class="bi-loading-mask" :data="data.params" :list="data.topList"
          :title="`${data.params.year}年国内多缸柴油发动机企业销量排名${data.params.subMarket ? '(' + data.params.subMarket + ')' : data.params.segment ? '(' + data.params.segment + ')' : '(总体)'}`" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <bar v-loading="loading.dataB" class="bi-loading-mask" titleIcon="data2"
          :title="`${data.params.year}年国内多缸柴油发动机企业份额${data.params.subMarket ? '(' + data.params.subMarket + ')' : data.params.segment ? '(' + data.params.segment + ')' : '(总体)'}`"
          :series="data.dataB" :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent1, params, {
                mapping: {
                  sales: 'sales',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                sortField: 'value',
                singleColumn: false
              })
          }" yAxisLabelFormate="{value}%" tooltip-units="%" y-axis-name="单位：(%)" :y-axis-max="100" :grid="{}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" height="57%" reverse-legend totalSortLegend />
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12">
        <lines v-loading="loading.dataC" class="bi-loading-mask" titleIcon="data3" :title="`国内多缸柴油发动机企业销量5年走势`"
          :series="data.dataC" y-axis-name="单位：(万台)" tooltip-units="万台"
          :grid="{ left: 46, bottom: 46, right: 46, top: 46 }" height="57%"
          :legend="{ selectedMode: 'multiple', selected: data.dataDSelected }" :reverse-legend="false" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <bar v-loading="loading.dataD" class="bi-loading-mask" titleIcon="data4" :title="`国内多缸柴油发动机企业份额5年走势`"
          :series="data.dataD" :tooltip="{
            formatter: params =>
              TooltipFormatter(TooltipComponent2, params, {
                mapping: {
                  sales: 'sales',
                  proportion: 'slice',
                  yoy: 'slice'
                },
                sortField: 'value',
                singleColumn: false
              })
          }" y-axis-name="单位：(%)" yAxisLabelFormate="{value}%" :y-axis-max="100" tooltip-units="%" :grid="{}"
          :legend="{ orient: 'vertical', bottom: 4, right: 4 }" :xAxis="{ nameTextStyle: { height: '220px' } }"
          height="57%" reverse-legend />
      </el-col>
    </el-row>

  </div>
</template>

<script setup lang="jsx">
import rank from './components/rank.vue'
import lines from '@/views/components/echarts/lines.vue'
import bar from '@/views/components/echarts/bar.vue'
import Tpis from '@/views/components/tooltip/index.vue'
import SearchFormResource from './components/SearchFormResource.vue'
import { TooltipFormatter } from '@/utils/common/method.js'
import { dictsResource } from '@/utils/common/dicts.js'
import BiContent from '@/views/components/tabs/BiContent/index.vue'
import { defaultDataBMonth, defaultDataBQuarter, fillXAxisList } from '../utils/defaultChartData.js'
import {
  topSalesList,
  corpSliceList,
  corpSalesSliceTrendList
} from '@/api/intelligence/cnEnginOrg.js'
import { dataConvertForLine } from '@/utils/dataconvert'
import { formatMonthRange } from '@/utils/safeStringUtils'
import { nextTick } from 'vue'
import { useRect } from "@/utils/hooks/useRect.js"
const { navBottom } = useRect()

const dataSource = dictsResource.find(v => v.label === '中内协')?.value

let defaultYearArray = []
const currentYear = new Date().getFullYear()
for (let i = 0; i < 5; i++) {
  defaultYearArray.push({ name: currentYear - i + '年', value: 0 })
}
defaultYearArray = defaultYearArray.reverse()
// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  id: '10', // top10
  dataSource: dataSource, // 数据来源（中内协）
  segment: '', // 板块
  subMarket: '' // 细分市场
}
const data = reactive({
  params: { ...originParams },
  topList: [], // 表1前几排行
  dataB: [], // 表2
  dataC: [], // 表3
  dataD: [], // 表4
  dataDSelected: {}
})
const loading = reactive({
  topList: false, // 表1前几排行
  dataB: false, // 表2
  dataC: false, // 表3
  dataD: false // 表4
})
/**
 * @description 点击搜索
 * @param params 搜索参数
 */
function getParams(params) {
  data.params = params
  initTopData(params)
  initChartBData(params)
  initChartCDData(params)
}

/**
 * @description 处理top10参数
 * @param params 搜索参数
 */
const initTopData = async params => {
  // if (loading.topList) return
  loading.topList = true
  const res = await topSalesList(params).catch(e => e)
  if (res.code !== 200) return (loading.topList = false)
  const list = res.data
  let maxSale = 0
  // 获取最大值sales; 转成字符串
  list.forEach(el => {
    if (el.sales > maxSale) maxSale = el.sales
    for (let i in el) {
      if (i === 'rankingContrast') continue
      el[i] = el[i] ? el[i] : '/'
    }
  })
  // 进度条百分比值salesPercent计算；第一名颜色处理
  maxSale = Math.ceil(maxSale)
  list.forEach(el => {
    if (el.engineManufacturer === '玉柴集团') {
      el.color = '#E72331'
    } else {
      el.color = '#9BA4AB'
    }
    el.salesPercent = el.sales === '/' ? 0 : ((el.sales / maxSale) * 100).toFixed(1) - 0
  })
  data.topList = list
  await nextTick()
  loading.topList = false
}
/**
 * @description 处理表2
 * @param params 搜索参数
 */
const initChartBData = async params => {
  loading.dataB = true
  const res = await corpSliceList(params).catch(e => e)

  if (res.code !== 200) return (loading.dataB = false)
  if (res.data['@type']) delete res.data['@type']
  let response = []
  // 处理数据
  console.log('dataB', res.data)

  const pointerType = data.params.pointerType // 指标类型(0-月，2-月累，1-季度)
  let list = []
  if (pointerType === '1') {
    const json = res.data
    if (Object.keys(json).length > 0) {
      const quarterArray = ['', '第一季度', '第二季度', '第三季度', '第四季度']
      for (let i in json) {
        const listIn = []
        json[i].forEach(e => {
          e.month = quarterArray[i]
        })
        listIn.push(...json[i])
        list.push(listIn)
      }
    }
  } else {
    list = jsonToArray(res.data)
  }
  console.log('list1', list)
  list.forEach(element => {
    if (element === null) return
    element.forEach(el => {
      if (el.slice) {
        el.slice = el.slice.replace(/%/g, '') - 0
      } else {
        el.slice = ''
      }

      if (pointerType === '1') {
        if (el.month === undefined) {
          el.month = '汇总'
        }
      } else {
        if (el.month === undefined) {
          el.month = '汇总'
        } else {
          el.month = el.month.toString().indexOf('月') !== -1 ? el.month : el.month + '月'
        }
      }
    })
  })
  console.log('list2', list)

  const allType = getAllType(list, 'engineManufacturer')
  const allTypeList = setXAxisHasAllType(
    list,
    {
      nameKey: 'engineManufacturer',
      dataNameKey: 'month',
      dataValueKey: 'slice',
      dataObj: {
        engineManufacturer: '',
        month: '',
        sales: '',
        slice: ''
      }
    },
    allType
  )

  response = setChartData(
    allTypeList,
    {
      nameKey: 'engineManufacturer',
      dataNameKey: 'month',
      dataValueKey: 'slice'
    },
    allType
  )
  const dataB = fillXAxisList(
    response,
    pointerType === '1' ? defaultDataBQuarter : defaultDataBMonth
  )
  // 判断dataB。data的子项value是否为0 为0则删除父项
  // for (let i = dataB.data.length - 1; i >= 0; i--) {
  //   if (dataB.data[i].value === 0) {
  //     dataB.data.splice(i, 1)
  //   }
  // }


  // data.dataB = dataB
  data.dataB = dataConvertForLine(dataB, 1)
  await nextTick()
  loading.dataB = false
}
/**
 * @description 处理表3/4
 * @param params 搜索参数
 */
const initChartCDData = async params => {
  // if (loading.dataC || loading.dataD) return
  loading.dataC = true
  loading.dataD = true
  const res = await corpSalesSliceTrendList(params).catch(e => e)
  if (res.code !== 200) return (loading.dataC = false), (loading.dataD = false)
  console.log('res.data', res.data)
  // 使用安全的工具函数处理 lastYearMonth
  const lastYearMonthRange = formatMonthRange(res.data.lastYearMonth)
  const saleList = res.data && res.data.saleList ? res.data.saleList : []
  const dataC = []
  saleList.forEach(element => {
    if (element && element.data && element.data.length > 0) {
      const dataList = []
      element.data.forEach((el, index) => {
        // 折线图最后一个年份需要展示月份处理
        // 补齐名称后的“年"
        el.year = el.year.indexOf('年') > -1 ? el.year : el.year + '年'
        dataList.push({
          name: index === element.data.length - 1 ? `${el.year}${lastYearMonthRange}` : el.year,
          value: el.sales === undefined || el.sales === null ? '' : el.sales - 0
        })
      })
      dataC.push({
        name: element.name,
        data: dataList
      })
    }
  })
  // 将其他项移动到数组最后一位
  let element = null
  dataC.forEach((item, index) => {
    if (item.name === '其他') {
      element = item
      dataC.splice(index, 1)
    }
  })
  dataC.push(element)

  let yearMap = res.data && res.data.yearMap ? res.data.yearMap : []
  if (yearMap['@type']) delete yearMap['@type']
  let list = jsonToArray(yearMap)
  list.forEach((element, index) => {
    // 柱状图最后一个年份需要展示月份处理
    element.forEach(el => {
      el.year = index === list.length - 1 ? `${el.year}年${lastYearMonthRange}` : `${el.year}年`
      if (el.slice) {
        el.slice = el.slice.replace(/%/g, '') - 0
      } else {
        el.slice = ''
      }
    })
  })
  const allType = getAllType(list, 'engineManufacturer')
  const allTypeList = setXAxisHasAllType(
    list,
    {
      nameKey: 'engineManufacturer',
      dataNameKey: 'year',
      dataValueKey: 'slice',
      dataObj: {
        engineManufacturer: '',
        year: '',
        sales: '',
        slice: ''
      }
    },
    allType
  )

  const dataD = setChartData(
    allTypeList,
    {
      nameKey: 'engineManufacturer',
      dataNameKey: 'year',
      dataValueKey: 'slice'
    },
    allType
  )
  const deepColorItems = ['云内', '潍柴', '潍柴控股', '康明斯', '玉柴集团', '玉柴']
  const selectedData = {}
  dataC.forEach(el => {
    el.type = 'line'
    if (deepColorItems.indexOf(el.name) !== -1) {
      selectedData[el.name] = true
    } else {
      selectedData[el.name] = false
    }
  })
  data.dataDSelected = selectedData
  // data.dataC = fillXAxisList(dataC, defaultYearArray)
  // data.dataD = fillXAxisList(dataD, defaultYearArray)
  data.dataC = dataConvertForLine(dataC, 1)
  await nextTick()
  loading.dataC = false
  data.dataD = dataConvertForLine(dataD, 1)
  await nextTick()
  loading.dataD = false
}

/**
 * step1.获取所有分类
 */
const getAllType = (list, key) => {
  const allName = []
  list.forEach(element => {
    if (element === null) return
    element.forEach(el => {
      allName.push(el[key])
    })
  })
  return Array.from(new Set(allName))
}

/**
 * step2.设置列表数据每个X轴含有所有分类
 */
const setXAxisHasAllType = (data, key, allNameArr) => {
  const list = JSON.parse(JSON.stringify(data))
  if (!list || (list && list.length === 0)) return []
  list.forEach(element => {
    if (!element) return
    if (element && element.length > 0) {
      const elementCopy = JSON.parse(JSON.stringify(element))
      allNameArr.forEach(el => {
        if (elementCopy.map(v => v[key.nameKey]).indexOf(el) === -1) {
          element.push({
            ...key.dataObj,
            [key.nameKey]: el,
            [key.dataNameKey]: elementCopy[0][key.dataNameKey],
            [key.dataValueKey]: ''
          })
        }
      })
    }
  })
  return list
}

/**
 * step3.生成charts数据
 */
const setChartData = (data, key, chartType) => {
  const response = []
  chartType.forEach(element => {
    // 生成分类
    const typeJson = {
      name: element,
      data: []
    }
    // 生成x轴
    data.forEach(el => {
      el.forEach(v => {
        if (v[key.nameKey] === element) {
          typeJson.data.push({ ...v, name: v[key.dataNameKey], value: v[key.dataValueKey] })
        }
      })
    })
    response.push(typeJson)
  })
  return response
}

// const fillXAxisList = (list, defaultList) => {
//   const response = JSON.parse(JSON.stringify(list))
//   if (response.length === 0) {
//     return [{ name: '', data: defaultList }]
//   }
//   response.forEach(element => {
//     const ElementList = element.data
//     defaultList.forEach((el, ind) => {
//       if (el.name !== ElementList[ind].name) {
//         ElementList.splice(ind, 0, { ...defaultList[ind] })
//       }
//     })
//   })
//   return response
// }
/**
 * JSON对象转数组
 * @param json json对象
 */
const jsonToArray = json => {
  let list = []
  if (Object.keys(json).length > 0) {
    for (let i in json) {
      list.push(json[i])
    }
  }
  return list
}
// 首次加载请求由头部组件处理好数据后发起
// initTopData(data.params)
// initChartBData(data.params)
// initChartCDData(data.params)

// const TooltipFormatter = (TooltipComponent,params,mapping) => {
//   return NodeToHtml(TooltipComponent, {
//     params,
//     mapping
//   })
// }
// Tooltip组件渲染
const TooltipComponent1 = ({ params, mapping, showTotal, shouldSort, singleColumn, sortField }) => {
  let result = params
  // 排序倒序
  result = result.sort.call(result, (a, b) => {
    return b.value - a.value
  })
  if (params && params.length >= 12) {
    result.pop()
  }


  return (
    <Tpis
      params={result}
      mapping={mapping}
      showTotal={showTotal}
      shouldSort={shouldSort}
      singleColumn={singleColumn}
      sortField={sortField}
    >
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {item.data.sales || 0}
                {item.axisValue.includes('汇总') ? '万台' : '台'} |{' '}
              </span>
              <span>{item.data.proportion || 0}% </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
const TooltipComponent2 = ({ params, mapping, showTotal, shouldSort, singleColumn, sortField }) => {
  // console.log('TooltipComponent', params)
  return (
    <Tpis
      params={params}
      mapping={mapping}
      showTotal={showTotal}
      shouldSort={shouldSort}
      singleColumn={singleColumn}
      sortField={sortField}
    >
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{item.data.installationcount || item.data.sales || 0}万台 | </span>
              <span>{item.data.proportion || 0}% </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}

:deep(.el-col) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.wrap {
  position: relative;
}
</style>
