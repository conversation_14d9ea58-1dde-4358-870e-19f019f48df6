<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="pointerType">
          <el-select
            v-model="params.pointerType"
            placeholder="指标类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <!-- TODO: 指标类型字典字典变换需要注意修改 -->
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <DictsResource
        :form="params"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            clearable: true
          },
          {
            name: '板块',
            key: 'segment'
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            key: 'subMarket2',
            disabled: data.disabledSubMarket2,
            hide: data.hideSubMarket2
          }
        ]"
        :dicts="data.linkageData"
        :propsBreed="{ name: '品系', key: 'breed', show: true, disabled: data.disabledBreed }"
        :propsWeightMidLight="{
          name: '重中轻',
          key: 'weightMidLight',
          show: !data.hideWeightMidLight,
          disabled: false
        }"
        :propsDataType="{
            name: '数据扩展',
            key: 'dataType',
            show: true
          }"
        :xs="8"
        :sm="8"
        :md="3"
      />
      <!-- <el-col
        v-if="params.dataSource === '1'"
        :xs="8"
        :sm="8"
        :md="params.dataType.length > 2 ? 5 : 3"
      >
        <el-form-item prop="dataType">
          <el-select
            v-model="params.dataType"
            multiple
            placeholder="数据扩展"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictDataType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col> -->
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item>
          <el-button type="primary" color="#115E93" @click="toggleSearch">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
      
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType, dictDataType } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'
import useSearchReset from '@/utils/hooks/useSearchReset.js'
const store = useStore()

// import formValidate from '@/utils/hooks/formValidate.js'
// const { disabledFeatureDate } = formValidate()

const props = defineProps({
  linkageData: [], // 多级联动数据
  params: {
    type: Object,
    required: true,
    default: () => ({
      year: '', // 年份
      month: '', // 月
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      quarter: '', // 季度
      dataSource: '', // 数据来源
      segment: '', // 板块
      subMarket1: '', // 细分市场1
      subMarket2: '', // 细分市场2
      engineFactory: '', // 发动机厂
      fuelType: '', // 燃料
      manuFacturer: '', // 主机厂
      breed: '', // 品系
      dataType: [], // 数据分类(汽油、微客、微改、微卡)
      weightMidLight: ''
    })
  }
})

const emit = defineEmits(['change'])

const data = reactive({
  hideSubMarket2: true,
  hideWeightMidLight: false,
  disabledSubMarket2: false,
  disabledBreed: false
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const {
  initDateRange,
  innerdate,
  disabledFeatureDate,
  newDictsMonthTotal,
  newDictsQuarter,
  newDictsMonth
} = useInnerData(params, toggleSearch)
// 在现有的搜索函数后添加这一行
const { resetSearch } = useSearchReset(params, toggleSearch)
// const currentMonth = (new Date().getMonth() + 1).toString() // 月
// const currentQuarter = getCurrentQuarter().toString() // 季度
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)
watch(
  () => params.dataSource,
  val => {
    // 需要将细分市场2 换成重中轻的，只有上险，货运，流向
    if (val === '1') {
      // 上险数
      initDateRange('上险数')
      params.segment = '商用车'
      params.subMarket1 = ''
      data.hideWeightMidLight = false
      data.hideSubMarket2 = true
      params.subMarket2 = ''
    } else if (val === '6') {
      // 货运新增数
      initDateRange('货运新增数')
      params.segment = '商用车'
      params.subMarket1 = '卡车'
      data.hideWeightMidLight = false
      data.hideSubMarket2 = true
      params.subMarket2 = ''
    } else if (val === '2') {
      // 货运新增数
      initDateRange('装机数')
      params.segment = '通机'
      params.subMarket1 = ''
      data.hideWeightMidLight = true
      data.hideSubMarket2 = false
      params.weightMidLight = ''
    }
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)
watch([() => params.subMarket2, () => params.breed], val => {
  const dataSource = params.dataSource
  if (dataSource !== '1' && dataSource !== '6') {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
    return
  }
  if (val[0] && !val[1]) {
    data.disabledSubMarket2 = false
    data.disabledBreed = true
  } else if (!val[0] && val[1]) {
    data.disabledSubMarket2 = true
    data.disabledBreed = false
  } else {
    data.disabledSubMarket2 = false
    data.disabledBreed = false
  }
})

/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
function toggleSearch() {
  if (params.dataSource !== '1') {
    params.dataType = []
  }
  const data = JSON.parse(JSON.stringify(toRaw(params)))
  // delete data.subMarket2
  emit('change', data)
}

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['上险数', '货运新增数', '装机数'],
      page: 'oem'
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}
initDateRange('上险数', true)
getDictsData()
</script>
