<template>
  <el-button 
    :icon="icon" 
    :type="type"
    :size="size"
    :loading="loading"
    @click="handleReset"
    v-bind="$attrs"
  >
    {{ text }}
  </el-button>
</template>

<script setup>
import { ref } from 'vue'

defineOptions({
  name: 'SearchResetButton',
  inheritAttrs: false
})

const props = defineProps({
  // 按钮文本
  text: {
    type: String,
    default: '重置'
  },
  // 按钮图标
  icon: {
    type: String,
    default: 'Refresh'
  },
  // 按钮类型
  type: {
    type: String,
    default: ''
  },
  // 按钮尺寸
  size: {
    type: String,
    default: ''
  },
  // 重置时是否显示加载状态
  showLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['reset', 'before-reset', 'after-reset'])

const loading = ref(false)

/**
 * 处理重置按钮点击
 */
const handleReset = async () => {
  try {
    if (props.showLoading) {
      loading.value = true
    }

    // 触发重置前事件
    emit('before-reset')

    // 触发重置事件
    await emit('reset')

    // 触发重置后事件
    emit('after-reset')
  } catch (error) {
    console.error('重置操作失败:', error)
  } finally {
    if (props.showLoading) {
      loading.value = false
    }
  }
}
</script>

<style scoped>
/* 可以根据项目需要添加自定义样式 */
</style>
