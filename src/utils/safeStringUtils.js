/**
 * 安全字符串处理工具函数
 * 用于处理可能是数字或字符串类型的数据，避免类型错误
 */

/**
 * 安全地对值执行字符串替换操作
 * @param {any} value - 要处理的值（可能是数字、字符串或其他类型）
 * @param {string|RegExp} searchValue - 要搜索的值
 * @param {string} replaceValue - 替换的值
 * @returns {string} - 处理后的字符串
 */
export const safeReplace = (value, searchValue, replaceValue) => {
  if (value === null || value === undefined) {
    return ''
  }
  
  // 确保转换为字符串
  const stringValue = String(value)
  return stringValue.replace(searchValue, replaceValue)
}

/**
 * 安全地处理月份范围字符串
 * 兼容数字和字符串类型的月份数据
 * @param {number|string} lastYearMonth - 最后年月数据
 * @returns {string} - 格式化后的月份范围字符串
 */
export const formatMonthRange = (lastYearMonth) => {
  if (lastYearMonth === null || lastYearMonth === undefined) {
    return '1月'
  }
  
  // 处理数字类型
  if (typeof lastYearMonth === 'number') {
    return lastYearMonth === 1 ? '1月' : `1-${lastYearMonth}月`
  }
  
  // 处理字符串类型
  if (typeof lastYearMonth === 'string') {
    // 如果已经包含"月"字符
    if (lastYearMonth.includes('月')) {
      return lastYearMonth === '1月' ? lastYearMonth : `1-${lastYearMonth.replace('月', '')}月`
    }
    // 如果是纯数字字符串
    const numValue = parseInt(lastYearMonth, 10)
    if (!isNaN(numValue)) {
      return numValue === 1 ? '1月' : `1-${numValue}月`
    }
    // 其他情况直接返回
    return lastYearMonth
  }
  
  // 默认返回
  return '1月'
}

/**
 * 安全地检查字符串是否包含某个子串
 * @param {any} value - 要检查的值
 * @param {string} searchString - 要搜索的子串
 * @returns {boolean} - 是否包含
 */
export const safeIncludes = (value, searchString) => {
  if (value === null || value === undefined) {
    return false
  }
  
  const stringValue = String(value)
  return stringValue.includes(searchString)
}

/**
 * 安全地获取字符串的索引位置
 * @param {any} value - 要检查的值
 * @param {string} searchString - 要搜索的子串
 * @returns {number} - 索引位置，未找到返回 -1
 */
export const safeIndexOf = (value, searchString) => {
  if (value === null || value === undefined) {
    return -1
  }
  
  const stringValue = String(value)
  return stringValue.indexOf(searchString)
}

/**
 * 安全地分割字符串
 * @param {any} value - 要分割的值
 * @param {string} separator - 分隔符
 * @returns {string[]} - 分割后的数组
 */
export const safeSplit = (value, separator) => {
  if (value === null || value === undefined) {
    return []
  }
  
  const stringValue = String(value)
  return stringValue.split(separator)
}

/**
 * 安全地转换为字符串
 * @param {any} value - 要转换的值
 * @param {string} defaultValue - 默认值
 * @returns {string} - 转换后的字符串
 */
export const safeToString = (value, defaultValue = '') => {
  if (value === null || value === undefined) {
    return defaultValue
  }
  
  return String(value)
}

/**
 * 安全地处理百分比字符串，移除百分号并转换为数字
 * @param {any} value - 要处理的值
 * @returns {number} - 处理后的数字
 */
export const safeParsePercentage = (value) => {
  if (value === null || value === undefined) {
    return 0
  }
  
  const stringValue = String(value)
  const numericValue = stringValue.replace(/%/g, '')
  const parsed = parseFloat(numericValue)
  
  return isNaN(parsed) ? 0 : parsed
}
