import { ref, toRaw, nextTick } from 'vue'

/**
 * 搜索表单重置功能的完整解决方案
 * 支持多种重置策略和自定义配置
 * 
 * @param {Object} config - 配置对象
 * @param {Object} config.params - 响应式的搜索参数对象
 * @param {Function} config.searchCallback - 搜索回调函数
 * @param {Object} config.initialValues - 初始值（可选，默认使用当前 params 值）
 * @param {Object} config.resetStrategy - 重置策略配置
 * @param {Function} config.customResetLogic - 自定义重置逻辑
 * @returns {Object} 返回重置相关的方法和状态
 */
export default function useSearchFormReset(config) {
  const {
    params,
    searchCallback,
    initialValues = null,
    resetStrategy = {},
    customResetLogic = null
  } = config

  // 默认重置策略
  const defaultResetStrategy = {
    autoSearch: true,           // 重置后自动搜索
    preserveFields: [],         // 需要保留的字段
    resetToEmpty: [],          // 需要重置为空的字段
    resetToDefault: {},        // 需要重置为特定默认值的字段
    clearArrays: true,         // 是否清空数组字段
    clearObjects: false        // 是否清空对象字段
  }

  const strategy = { ...defaultResetStrategy, ...resetStrategy }
  
  // 保存初始参数值
  const savedInitialValues = ref(initialValues || { ...toRaw(params) })

  /**
   * 执行重置操作
   */
  const resetSearch = async () => {
    try {
      // 如果有自定义重置逻辑，优先使用
      if (customResetLogic && typeof customResetLogic === 'function') {
        await customResetLogic(params, savedInitialValues.value)
      } else {
        // 使用默认重置逻辑
        await executeDefaultReset()
      }

      // 等待 DOM 更新
      await nextTick()

      // 自动执行搜索
      if (strategy.autoSearch && searchCallback && typeof searchCallback === 'function') {
        await searchCallback()
      }
    } catch (error) {
      console.error('重置搜索条件时发生错误:', error)
      throw error
    }
  }

  /**
   * 执行默认重置逻辑
   */
  const executeDefaultReset = async () => {
    const initialData = { ...toRaw(savedInitialValues.value) }
    
    // 遍历参数对象进行重置
    for (const key in params) {
      // 跳过需要保留的字段
      if (strategy.preserveFields.includes(key)) {
        continue
      }

      // 重置为空的字段
      if (strategy.resetToEmpty.includes(key)) {
        params[key] = getEmptyValue(params[key])
        continue
      }

      // 重置为特定默认值的字段
      if (strategy.resetToDefault.hasOwnProperty(key)) {
        params[key] = strategy.resetToDefault[key]
        continue
      }

      // 使用初始值重置
      if (initialData.hasOwnProperty(key)) {
        params[key] = getResetValue(initialData[key], params[key])
      }
    }
  }

  /**
   * 获取空值
   */
  const getEmptyValue = (currentValue) => {
    if (Array.isArray(currentValue)) {
      return []
    }
    if (typeof currentValue === 'object' && currentValue !== null) {
      return {}
    }
    if (typeof currentValue === 'number') {
      return 0
    }
    if (typeof currentValue === 'boolean') {
      return false
    }
    return ''
  }

  /**
   * 获取重置值
   */
  const getResetValue = (initialValue, currentValue) => {
    // 数组处理
    if (Array.isArray(currentValue)) {
      return strategy.clearArrays ? [] : [...(initialValue || [])]
    }
    
    // 对象处理
    if (typeof currentValue === 'object' && currentValue !== null) {
      return strategy.clearObjects ? {} : { ...(initialValue || {}) }
    }
    
    // 基本类型直接返回初始值
    return initialValue
  }

  /**
   * 更新保存的初始值
   */
  const updateInitialValues = (newInitialValues) => {
    savedInitialValues.value = { ...toRaw(newInitialValues) }
  }

  /**
   * 获取当前保存的初始值
   */
  const getInitialValues = () => {
    return { ...toRaw(savedInitialValues.value) }
  }

  /**
   * 重置特定字段
   */
  const resetField = (fieldName) => {
    const initialData = savedInitialValues.value
    if (initialData.hasOwnProperty(fieldName)) {
      params[fieldName] = getResetValue(initialData[fieldName], params[fieldName])
    }
  }

  /**
   * 重置多个字段
   */
  const resetFields = (fieldNames) => {
    fieldNames.forEach(fieldName => {
      resetField(fieldName)
    })
  }

  return {
    resetSearch,
    resetField,
    resetFields,
    updateInitialValues,
    getInitialValues,
    savedInitialValues: savedInitialValues.value
  }
}
