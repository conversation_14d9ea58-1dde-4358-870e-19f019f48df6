import { ref, toRaw } from 'vue'
import { getAllDateRange } from '@/utils/all-date-range.js'

/**
 * 通用搜索表单重置功能 Hook
 * 提供最小侵入的方式为搜索表单添加重置功能
 *
 * @param {Object} params - 响应式的搜索参数对象
 * @param {Function} searchCallback - 搜索回调函数
 * @param {Object} options - 可选配置
 * @param {Object} options.initialValues - 自定义初始值，如果不提供则使用当前 params 的值
 * @param {Function} options.beforeReset - 重置前的回调函数
 * @param {Function} options.afterReset - 重置后的回调函数
 * @param {boolean} options.autoSearch - 重置后是否自动搜索，默认为 true
 * @param {Object} options.dynamicFields - 动态字段配置，用于在重置时动态计算字段值
 * @param {Object} options.dateRange - 日期范围对象，包含 maxYear 等信息
 * @returns {Object} 返回重置相关的方法和状态
 */
export default function useSearchReset(params, searchCallback, options = {}) {
  const {
    initialValues = null,
    beforeReset = null,
    afterReset = null,
    autoSearch = true,
    dynamicFields = {}
  } = options

  // 保存初始参数值
  const savedInitialValues = ref(initialValues || { ...toRaw(params) })

  /**
   * 重置搜索条件到初始值
   */
  const resetSearch = async () => {
    const { minYear, maxYear, minMonth, maxMonth } = await getAllDateRange('中内协')
    const dateRange ={
      maxYear,minMonth,maxMonth,minYear
    }
    console.log('指标类型', dateRange)
    try {
      // 执行重置前回调
      if (beforeReset && typeof beforeReset === 'function') {
        await beforeReset()
      }

      // 重置参数到初始值
      const resetValues = { ...toRaw(savedInitialValues.value) }

      // 处理动态字段
      if (dynamicFields && Object.keys(dynamicFields).length > 0) {
        for (const [fieldName, valueGetter] of Object.entries(dynamicFields)) {
          if (typeof valueGetter === 'function') {
            resetValues[fieldName] = valueGetter()
          } else {
            resetValues[fieldName] = valueGetter
          }
        }
      }

      // 特殊处理年份字段 - 如果有 dateRange 且包含 maxYear
      if (dateRange && dateRange.maxYear && resetValues.hasOwnProperty('year')) {
        resetValues.year = dateRange.maxYear
      }
      // 特殊处理年份字段 - 如果有 dateRange 且包含 maxYear
      if (dateRange && dateRange.maxMonth && resetValues.hasOwnProperty('month')) {
        resetValues.month = dateRange.maxMonth
      }

      Object.assign(params, resetValues)

      // 执行重置后回调
      if (afterReset && typeof afterReset === 'function') {
        await afterReset()
      }

      // 自动执行搜索
      if (autoSearch && searchCallback && typeof searchCallback === 'function') {
        await searchCallback()
      }
    } catch (error) {
      console.error('重置搜索条件时发生错误:', error)
    }
  }

  /**
   * 更新保存的初始值
   * @param {Object} newInitialValues - 新的初始值
   */
  const updateInitialValues = newInitialValues => {
    savedInitialValues.value = { ...toRaw(newInitialValues) }
  }

  /**
   * 获取当前保存的初始值
   */
  const getInitialValues = () => {
    return { ...toRaw(savedInitialValues.value) }
  }

  return {
    resetSearch,
    updateInitialValues,
    getInitialValues,
    savedInitialValues: savedInitialValues.value
  }
}
