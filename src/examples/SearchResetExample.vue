<template>
  <div class="search-reset-example">
    <h2>搜索表单重置功能示例</h2>
    
    <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
      <el-row :gutter="16">
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="year">
            <el-date-picker
              v-model="params.year"
              type="year"
              value-format="YYYY"
              format="YYYY"
              placeholder="年份"
              style="width: 100%"
              :clearable="false"
            />
          </el-form-item>
        </el-col>
        
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="dataSource">
            <el-select v-model="params.dataSource" placeholder="数据来源" style="width: 100%">
              <el-option label="上险数" value="1" />
              <el-option label="货运新增数" value="6" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :xs="8" :sm="8" :md="3">
          <el-form-item prop="province">
            <el-select v-model="params.province" placeholder="省份" clearable style="width: 100%">
              <el-option label="北京" value="beijing" />
              <el-option label="上海" value="shanghai" />
              <el-option label="广东" value="guangdong" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :xs="8" :sm="8" :md="6">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button icon="Refresh" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <div class="result-display">
      <h3>当前搜索参数：</h3>
      <pre>{{ JSON.stringify(params, null, 2) }}</pre>
      
      <h3>dateRange 信息：</h3>
      <pre>{{ JSON.stringify(dateRange, null, 2) }}</pre>
      
      <div class="tips">
        <h3>测试说明：</h3>
        <ul>
          <li>修改任意搜索条件后点击"重置"按钮</li>
          <li>观察年份是否重置为 dateRange.maxYear</li>
          <li>观察其他字段是否重置为初始值</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import useInnerData from '@/utils/hooks/innerData.js'
import useSearchReset from '@/utils/hooks/useSearchReset.js'

// 模拟搜索参数
const params = reactive({
  year: '2023',
  dataSource: '6',
  province: ''
})

// 模拟搜索函数
const handleSearch = () => {
  console.log('执行搜索，参数：', params)
}

// 使用 useInnerData Hook（模拟获取日期范围）
const { dateRange } = useInnerData(params, handleSearch)

// 使用重置功能，支持动态年份
const { resetSearch } = useSearchReset(params, handleSearch, {
  dateRange, // 传入 dateRange，重置时年份会自动设置为 maxYear
  beforeReset: () => {
    console.log('准备重置搜索条件')
  },
  afterReset: () => {
    console.log('重置完成')
  }
})

// 初始化日期范围（模拟数据源为货运新增数）
// 这里应该根据实际的数据源来初始化
// initDateRange('货运新增数', true)
</script>

<style scoped>
.search-reset-example {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.result-display {
  margin-top: 20px;
}

.result-display h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #303133;
}

.result-display pre {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 12px;
}

.tips {
  margin-top: 20px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.tips ul {
  margin: 10px 0;
  padding-left: 20px;
}

.tips li {
  margin: 5px 0;
  color: #606266;
}
</style>
